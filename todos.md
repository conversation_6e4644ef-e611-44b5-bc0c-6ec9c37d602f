creator dashboard is not mobile responsive and content is not visible cause navigation takes up too much space. make minimal and simple changes. dont change too much. dont do any breaking changes.

the mobile: go live view:  the end stream button above the camera preview is overlapping with the text before and going out of view. make minimal and simple changes. dont change too much. dont do any breaking changes.

the mobile: go live view:  make the text you are live above the preview a one liner and no card. remove the you are live part and leave only the notice. make minimal and simple changes. dont change too much. dont do any breaking changes.

the mobile: go live view (when broadcasting) is  slightly zoomed in on mobile when entering. make minimal and simple changes. dont change too much. dont do any breaking changes.

remove camera preview badge on mobile

reorder the go live page (stat card at end)
clean code and remove comments
remove console logs and debugging
mint price back