creator dashboard is not mobile responsive and content is not visible cause navigation takes up too much space. make minimal and simple changes. dont change too much. dont do any breaking changes.

the mobile: go live view:  the end stream button above the camera preview is overlapping with the text before and going out of view. make minimal and simple changes. dont change too much. dont do any breaking changes.

the mobile: go live view:  make the text you are live above the preview a one liner and no card. remove the you are live part and leave only the notice. add in smaller below that it can take up to 2 minutes until the stream appears for others). make minimal and simple changes. dont change too much. dont do any breaking changes.


reorder the go live page (stat card at end)
clean code and remove comments
remove console logs and debugging
mint price back



DONE
--
the mobile: go live view (when broadcasting) is  slightly zoomed in on mobile when entering. make minimal and simple changes. dont change too much. dont do any breaking changes.

broadcast / camera preview badge


IDK
--
when starting a stream on go live page with every stream created the creation of the stream takes longer next time. first it fired the broadcast to live on livepeer immediately, then 1 minute, then 2, then 5 and then never. check the broadcasting and what could be the issue if theres a problematic loop or something. make minimal and simple changes. dont change too much. dont do any breaking changes.