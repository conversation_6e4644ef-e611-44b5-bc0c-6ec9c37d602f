'use client';

/* eslint-disable @typescript-eslint/no-unused-vars */

import React, { useState, useEffect, useRef } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { useParams } from 'next/navigation';
import { Video, VideoOff, Mic, MicOff, Play, Square, Settings, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '../ui/textarea';
import { useToast } from '../ui/toast';
import { z } from 'zod';
import * as Broadcast from '@livepeer/react/broadcast';
import { getIngest } from '@livepeer/react/external';
import { CameraPreview } from './CameraPreview';

interface StreamData {
  id: string;
  playbackId?: string;
  ingestUrl?: string;
  streamKey?: string;
  title?: string;
  description?: string;
}

interface LiveStreamProps {
  onStreamStart?: (streamData: StreamData) => void;
  onStreamEnd?: () => void;
  onStopStream?: () => void;
  onBroadcastStart?: () => void;
}

const streamInfoSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters').max(100),
  description: z.string().optional(),
});

export function LiveStream({ onStreamStart, onBroadcastStart }: LiveStreamProps) {
  const { publicKey, connected } = useWallet();
  const params = useParams();
  const locale = params.locale as string || 'en';
  const { toast } = useToast();

  // Stream state
  const [isLive, setIsLive] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [streamKey, setStreamKey] = useState<string | null>(null);
  // const [playbackId, setPlaybackId] = useState<string | null>(null);
  const [streamId, setStreamId] = useState<string | null>(null);
  const [streamInfo, setStreamInfo] = useState({
    title: '',
    description: ''
  });
  const [streamInfoErrors, setStreamInfoErrors] = useState<Record<string, string>>({});

  // Device permissions
  const [hasCamera, setHasCamera] = useState(false);
  const [hasMicrophone, setHasMicrophone] = useState(false);
  const [cameraEnabled, setCameraEnabled] = useState(true);
  const [microphoneEnabled, setMicrophoneEnabled] = useState(true);
  
  // Preview state
  const [showPreview, setShowPreview] = useState(false);
  
  // Broadcasting state
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const broadcastRef = useRef<any>(null); // Livepeer Broadcast element type
  const [isBroadcasting, setIsBroadcasting] = useState(false);
  const [broadcastError, setBroadcastError] = useState<string | null>(null);
  const [shouldRenderBroadcast, setShouldRenderBroadcast] = useState(false);
  const [broadcastKey] = useState(0); // Force remount key

  // Media state
  const [mediaStream, setMediaStream] = useState<MediaStream | null>(null);
  const [videoTrack, setVideoTrack] = useState<MediaStreamTrack | null>(null);
  const [audioTrack, setAudioTrack] = useState<MediaStreamTrack | null>(null);

  // Check device permissions on mount and monitor stream
  useEffect(() => {
    checkDevicePermissions();
    
    // Clean up on component unmount
    return () => {
      // Stop any active streams
      if (isLive && streamKey) {
        stopStream();
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Only run on mount/unmount

  // Stream status monitoring
  useEffect(() => {
    let intervalId: NodeJS.Timeout;
    
    // If we're live, periodically check if the stream is actually active
    if (isLive && streamKey) {
      intervalId = setInterval(async () => {
        try {
          // Check if we're still broadcasting (client-side check)
          if (!isBroadcasting && broadcastRef.current) {
            // Force a refresh of the broadcast component
            setIsBroadcasting(true);
          }
          
        } catch (error) {
          console.error('Error checking stream status:', error);
        }
      }, 15000); // Check every 15 seconds
    }
    
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isLive, streamKey, isBroadcasting]);

  // Media cleanup effect - ensures browser recording stops when stream ends
  useEffect(() => {
    if (!streamKey || !isLive) {
      // Immediately hide the Broadcast component to force cleanup
      setShouldRenderBroadcast(false);
      
      
      // When stream is not live, aggressively stop all media tracks
      const stopAllMediaTracks = async () => {
        try {
          // Stop any existing media streams we have references to
          if (mediaStream) {
            mediaStream.getTracks().forEach(track => {
              track.stop();
            });
            setMediaStream(null);
          }

          if (videoTrack && videoTrack.readyState === 'live') {
            videoTrack.stop();
            setVideoTrack(null);
          }

          if (audioTrack && audioTrack.readyState === 'live') {
            audioTrack.stop();
            setAudioTrack(null);
          }

          // Force stop any getUserMedia streams that might still be active
          try {
            const stream = await navigator.mediaDevices.getUserMedia({ 
              video: hasCamera, 
              audio: hasMicrophone 
            });
            stream.getTracks().forEach(track => {
              track.stop();
            });
          } catch (error) {
          }
        } catch (error) {
        }
      };

      stopAllMediaTracks();
    } else if (streamKey && isLive) {
      // When going live, enable broadcast rendering
      setShouldRenderBroadcast(true);
    }
  }, [streamKey, isLive, mediaStream, videoTrack, audioTrack, hasCamera, hasMicrophone]);

  const checkDevicePermissions = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      setHasCamera(true);
      setHasMicrophone(true);
      setMediaStream(stream);
      const videoTracks = stream.getVideoTracks();
      const audioTracks = stream.getAudioTracks();
      setVideoTrack(videoTracks[0] || null);
      setAudioTrack(audioTracks[0] || null);
      stream.getTracks().forEach(track => track.stop());
      
      // Auto-show preview when devices are available
      setShowPreview(true);
    } catch (error) {
      try {
        const videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
        setHasCamera(true);
        setMediaStream(videoStream);
        const videoTracks = videoStream.getVideoTracks();
        setVideoTrack(videoTracks[0] || null);
        videoStream.getTracks().forEach(track => track.stop());
        
        // Show preview if at least camera is available
        setShowPreview(true);
      } catch (videoError) {
        setHasCamera(false);
      }
      
      try {
        const audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
        setHasMicrophone(true);
        setMediaStream(audioStream);
        const audioTracks = audioStream.getAudioTracks();
        setAudioTrack(audioTracks[0] || null);
        audioStream.getTracks().forEach(track => track.stop());
        
        // Show preview if at least microphone is available
        if (!hasCamera) {
          setShowPreview(true);
        }
      } catch (audioError) {
        setHasMicrophone(false);
      }
    }
  };

  const validateStreamInfo = () => {
    try {
      streamInfoSchema.parse(streamInfo);
      setStreamInfoErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach((err) => {
          if (err.path[0]) {
            errors[err.path[0] as string] = err.message;
          }
        });
        setStreamInfoErrors(errors);
      }
      return false;
    }
  };

  const createLivepeerStream = async () => {
    try {
      if (!publicKey) {
        throw new Error('Wallet not connected');
      }
      
      // Try to use centralized authentication system for API call
      let authHeaders = {};
      try {
        const { createAuthHeaders } = await import('@/lib/wallet-auth');
        authHeaders = createAuthHeaders(publicKey.toString());
      } catch (authError) {
        authHeaders = { 'x-wallet-address': publicKey.toString() };
      }
      
      const response = await fetch(`/${locale}/api/streams/create`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...authHeaders,
        },
        body: JSON.stringify({
          name: streamInfo.title,
          description: streamInfo.description || '',
          wallet_address: publicKey.toString(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `Failed to create stream: ${response.status}`);
      }

      const data = await response.json();
      
      // Handle both response formats for flexibility
      if (data.stream) {
        // New format - preferred
        setStreamKey(data.stream.livepeer.streamKey);
        // setPlaybackId(data.stream.livepeer.playbackId);
        setStreamId(data.stream.id); // Store the stream ID for ending the stream
        
        // Return complete data including livepeer info
        return {
          ...data.stream,
          livepeer: data.stream.livepeer
        };
      } else if (data.streamKey) {
        // Old format fallback - but we need the ID for chat!
        setStreamKey(data.streamKey);
        // setPlaybackId(data.playbackId);
        
        
        // Try to extract ID from any available field
        const possibleId = data.id || data.stream_id || data.streamId;
        if (possibleId) {
          setStreamId(possibleId);
        }
        
        // Return old format structure but try to include ID
        return {
          id: possibleId, // Try to include ID for chat
          streamKey: data.streamKey,
          playbackId: data.playbackId,
          title: streamInfo.title
        };
      } else {
        return null;
      }
    } catch (error) {
      // Add more detailed logging to help diagnose issues
      if (error instanceof Error) {
      }
      throw error;
    }
  };

  const startStream = async () => {
    // Prevent multiple concurrent stream creation attempts
    if (isConnecting || isLive) {
      return;
    }

    if (!connected || !publicKey) {
      alert('❌ Wallet not connected');
      toast({
        title: 'Wallet not connected',
        description: 'Please connect your wallet first',
        variant: 'destructive',
      });
      return;
    }

    if (!validateStreamInfo()) {
      toast({
        title: 'Invalid stream info',
        description: 'Please check required fields',
        variant: 'destructive',
      });
      return;
    }

    if (!hasCamera && !hasMicrophone) {
      toast({
        title: 'No devices available',
        description: 'Camera or microphone required',
        variant: 'destructive',
      });
      return;
    }

    // First check media permissions again to ensure camera/mic are ready
    try {
      // Quick test that media permissions are still good
      const testStream = await navigator.mediaDevices.getUserMedia({
        video: cameraEnabled,
        audio: microphoneEnabled
      });
      
      // Always release test stream
      testStream.getTracks().forEach(track => track.stop());
    } catch (err) {
      toast({
        title: 'Camera/Mic access denied',
        description: 'Please allow camera and microphone access in your browser',
        variant: 'destructive',
      });
      return;
    }

    setIsConnecting(true);
    setBroadcastError(null);

    try {
      // Reset broadcast state
      setIsBroadcasting(false);

      const streamData = await createLivepeerStream();

      setIsLive(true);
      onStreamStart?.(streamData);
      
      // Schedule a check after 5 seconds to see if broadcasting started successfully
      setTimeout(() => {
        if (isLive && !isBroadcasting) {
          setBroadcastError('Stream not connecting to server');
        }
      }, 5000);
      
      toast({
        title: 'Stream started',
        description: 'You are now broadcasting live',
      });
    } catch (error) {
      toast({
        title: 'Failed to start stream',
        description: error instanceof Error ? error.message : 'Please try again',
        variant: 'destructive',
      });
    } finally {
      setIsConnecting(false);
    }
  };

  const stopStream = async () => {
    try {
      // Properly clean up resources without page reload
      const tempStreamId = streamId;

      // First stop all media tracks
      if (mediaStream) {
        mediaStream.getTracks().forEach(track => {
          track.stop();
        });
        setMediaStream(null);
      }

      if (videoTrack) {
        videoTrack.stop();
        setVideoTrack(null);
      }

      if (audioTrack) {
        audioTrack.stop();
        setAudioTrack(null);
      }

      // Reset broadcast state
      setIsBroadcasting(false);
      setShouldRenderBroadcast(false);

      // Update database
      if (tempStreamId && publicKey) {
        try {
          // Try to use centralized authentication system for API call
          let authHeaders = {};
          try {
            const { createAuthHeaders } = await import('@/lib/wallet-auth');
            authHeaders = createAuthHeaders(publicKey.toString());
          } catch (authError) {
            authHeaders = { 'x-wallet-address': publicKey.toString() };
          }

          await fetch(`/${locale}/api/streams/${tempStreamId}/end`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              ...authHeaders,
            },
            body: JSON.stringify({
              streamKey: null,
              wallet_address: publicKey.toString(),
            }),
          });
        } catch (apiError) {
          console.error('Error ending stream:', apiError);
        }
      }

      // Reset all state
      setIsLive(false);
      setStreamKey(null);
      setStreamId(null);

      // Show success feedback
      toast({
        title: 'Stream ended',
        description: 'Stream has been stopped successfully',
      });
    } catch (error) {
      // Force stop even if there's an error
      setIsLive(false);
      setShouldRenderBroadcast(false);
      
      toast({
        title: 'Stream ended',
        description: 'Recording stopped (with errors)',
        variant: 'destructive',
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setStreamInfo(prev => ({ ...prev, [field]: value }));
    if (streamInfoErrors[field]) {
      setStreamInfoErrors(prev => {
        const errors = { ...prev };
        delete errors[field];
        return errors;
      });
    }
  };

  // Debug helper function
  const debugStreamStatus = () => {
    
    // Check browser capabilities
    
    // Force check device status
    navigator.mediaDevices.enumerateDevices()
      .then(devices => {
        const videoDevices = devices.filter(device => device.kind === 'videoinput');
        const audioDevices = devices.filter(device => device.kind === 'audioinput');
        
        videoDevices.forEach(() => {
          // Device information not logged in production
        });
        
        audioDevices.forEach(() => {
          // Device information not logged in production
        });
      })
      .catch(() => {});  // Ignore device enumeration errors
  };

  if (!connected) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Video className="h-5 w-5" />
            Live Streaming
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Connect your wallet to start streaming
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stream Setup */}
      {!isLive && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Stream Setup
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Stream Information */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Stream Title</label>
                <Input
                  value={streamInfo.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="Enter stream title"
                  className={streamInfoErrors.title ? 'border-red-500' : ''}
                />
                {streamInfoErrors.title && (
                  <p className="text-sm text-red-500 mt-1">{streamInfoErrors.title}</p>
                )}
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={streamInfo.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Enter description (optional)"
                  rows={3}
                />
              </div>
            </div>

            {/* Device Status */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Devices</h4>
              <div className="flex gap-4">
                <div className="flex items-center gap-2">
                  {hasCamera ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="text-sm">Camera</span>
                </div>
                <div className="flex items-center gap-2">
                  {hasMicrophone ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-500" />
                  )}
                  <span className="text-sm">Microphone</span>
                </div>
              </div>
            </div>

            {/* Start Stream Button */}
            <Button
              onClick={() => {
                startStream();
              }}
              disabled={isConnecting || (!hasCamera && !hasMicrophone)}
              className="w-full"
              size="lg"
            >
              {isConnecting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Connecting...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4 mr-2" />
                  Start Stream
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Camera Preview */}
      {!isLive && (
        <CameraPreview
          enabled={showPreview}
          cameraEnabled={cameraEnabled}
          microphoneEnabled={microphoneEnabled}
          hasCamera={hasCamera}
          hasMicrophone={hasMicrophone}
          onCameraToggle={() => setCameraEnabled(!cameraEnabled)}
          onMicrophoneToggle={() => setMicrophoneEnabled(!microphoneEnabled)}
        />
      )}

      {/* Live Stream View */}
      {isLive && streamKey && (
        <div className="space-y-4">
          {/* Stream Status */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant={isBroadcasting ? "destructive" : "outline"} className={isBroadcasting ? "animate-pulse" : ""}>
                    {isBroadcasting ? "● LIVE" : "◯ CONNECTING"}
                  </Badge>
                  <span className="font-medium">{streamInfo.title}</span>
                  
                  {broadcastError && (
                    <Badge variant="outline" className="text-yellow-500 ml-2">
                      {broadcastError}
                    </Badge>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button
                    onClick={() => {
                      debugStreamStatus();
                      // Force reconnect broadcast attempt
                      if (broadcastRef.current) {
                        toast({
                          title: 'Reconnecting broadcast',
                          description: 'Attempting to reconnect to stream...'
                        });
                        setIsBroadcasting(false);
                        setTimeout(() => setIsBroadcasting(true), 1000);
                      }
                    }}
                    variant="outline"
                    size="sm"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Debug
                  </Button>
                
                  <Button
                    onClick={stopStream}
                    variant="destructive"
                    size="sm"
                  >
                    <Square className="h-4 w-4 mr-2" />
                    End Stream
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Livepeer Broadcast Component */}
          {shouldRenderBroadcast && isLive && streamKey && (
            <Card key={`broadcast-card-${broadcastKey}`}>
              <CardContent className="p-0">
                <Broadcast.Root
                key={`broadcast-root-${broadcastKey}-${streamKey}`} // Unique key that changes on stop
                ingestUrl={getIngest(streamKey)} 
                aspectRatio={16 / 9}
                forceEnabled={true}
              >
                <div className="relative bg-gray-900 rounded-lg overflow-hidden">
                  {/* Main video element */}
                  <Broadcast.Video
                    className="w-full h-full object-cover"
                    title={streamInfo.title}
                    onLoadedData={() => {
                      setIsBroadcasting(true);
                      onBroadcastStart?.(); // Notify parent that broadcasting has actually started
                    }}
                    onError={(_error: React.SyntheticEvent<HTMLVideoElement, Event>) => {
                      setBroadcastError('Error displaying video feed');
                      toast({
                        title: 'Broadcast error',
                        description: 'An error occurred during broadcast',
                        variant: 'destructive',
                      });
                    }}
                  />

                  {/* Live indicator */}
                  <Broadcast.LoadingIndicator asChild>
                    <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center gap-2">
                      <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                      LIVE
                    </div>
                  </Broadcast.LoadingIndicator>

                  {/* Stream controls overlay */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                    <div className="flex items-center gap-2 bg-black/50 rounded-lg p-2">
                      {/* Microphone Toggle */}
                      <Button
                        size="sm"
                        variant={microphoneEnabled ? "default" : "outline"}
                        onClick={() => {
                          setMicrophoneEnabled(!microphoneEnabled);
                          // When toggling mic, we need to reconnect the broadcast
                          if (broadcastRef.current) {
                            try {
                              if (!microphoneEnabled) {
                                // Re-enable audio
                                broadcastRef.current.enableAudio?.();
                              } else {
                                // Disable audio
                                broadcastRef.current.disableAudio?.();
                              }
                            } catch (err) {
                            }
                          }
                        }}
                        disabled={!hasMicrophone}
                      >
                        {microphoneEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                      </Button>

                      {/* Camera Toggle */}
                      <Button
                        size="sm"
                        variant={cameraEnabled ? "default" : "outline"}
                        onClick={() => {
                          setCameraEnabled(!cameraEnabled);
                          // When toggling camera, we need to reconnect the broadcast
                          if (broadcastRef.current) {
                            try {
                              if (!cameraEnabled) {
                                // Re-enable video
                                broadcastRef.current.enableVideo?.();
                              } else {
                                // Disable video
                                broadcastRef.current.disableVideo?.();
                              }
                            } catch (err) {
                            }
                          }
                        }}
                        disabled={!hasCamera}
                      >
                        {cameraEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>
                </div>
              </Broadcast.Root>
            </CardContent>
          </Card>
          )}
        </div>
      )}

      {/* Device Permission Warnings */}
      {(!hasCamera || !hasMicrophone) && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {!hasCamera && !hasMicrophone
              ? 'No camera or microphone detected. Please allow access to devices.'
              : !hasCamera
              ? 'No camera detected. Video streaming will not be available.'
              : 'No microphone detected. Audio streaming will not be available.'
            }
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
